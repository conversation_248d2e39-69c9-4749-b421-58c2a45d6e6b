spring:
  application:
    name: youtube-consumer
  
  # Kafka configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    consumer:
      group-id: youtube-analytics-group-v3
      auto-offset-reset: latest
      enable-auto-commit: true
      auto-commit-interval: 1000
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"
        spring.json.use.type.headers: true

# Server configuration
server:
  port: 8082

# Logging configuration
logging:
  level:
    com.youtube.consumer: DEBUG
    org.springframework.kafka: INFO 