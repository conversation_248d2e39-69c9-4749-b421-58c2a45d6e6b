# 🚀 YouTube Analytics System - Setup Guide

## 📋 Prerequisites

Before starting, make sure you have the following installed:

1. **Docker & Docker Compose**
   - Download from: https://www.docker.com/products/docker-desktop
   - Verify installation: `docker --version` and `docker-compose --version`

2. **Java 17 or higher** (for local development)
   - Download from: https://adoptium.net/
   - Verify installation: `java --version`

3. **Maven** (usually comes with most Java IDEs)
   - Download from: https://maven.apache.org/download.cgi
   - Verify installation: `mvn --version`

---

## 🔑 API Keys Setup

### 1. YouTube Data API v3 Key

1. Go to [Google Cloud Console](https://console.developers.google.com/)
2. Create a new project or select existing one
3. Enable the **YouTube Data API v3**
4. Go to **Credentials** → **Create Credentials** → **API Key**
5. Copy the API key

### 2. Telegram Bot Token

1. Open Telegram and search for **@<PERSON>t<PERSON>ather**
2. Start a conversation and type `/newbot`
3. Follow the instructions to create your bot
4. Copy the bot token provided by <PERSON><PERSON><PERSON><PERSON>
5. **Important**: Start a conversation with your bot and send any message to activate it

---

## ⚙️ Project Setup

### 1. Clone and Navigate
```bash
# If you haven't already
cd "your-project-directory"
```

### 2. Create Environment File
```bash
# Copy the example environment file
cp env.example .env

# Edit the .env file with your API keys
# On Windows: notepad .env
# On Mac/Linux: nano .env
```

Update the `.env` file with your actual keys:
```
YOUTUBE_API_KEY=your_actual_youtube_api_key
TELEGRAM_BOT_TOKEN=your_actual_telegram_bot_token
```

### 3. Build Shared Library First
```bash
# Build the shared models library
cd shared
mvn clean install
cd ..
```

---

## 🐳 Running with Docker

### 1. Start Infrastructure Services (MySQL + Kafka)
```bash
# Start MySQL and Kafka first
docker-compose up -d mysql zookeeper kafka
```

### 2. Wait for Services to Start
Wait about 30-60 seconds for Kafka and MySQL to fully initialize.

### 3. Start Applications
```bash
# Start all applications
docker-compose up -d producer-app consumer-app telegram-bot
```

### 4. Check Logs
```bash
# Check if everything is running
docker-compose ps

# View logs for specific service
docker-compose logs producer-app
docker-compose logs consumer-app
docker-compose logs telegram-bot
```

---

## 🌐 Access Applications

- **Producer Web App**: http://localhost:8080
- **Consumer Analytics Dashboard**: http://localhost:8081
- **Telegram Bot**: Your bot should be active and responding

---

## 🔧 Local Development (Alternative)

If you prefer to run without Docker:

### 1. Start Infrastructure Only
```bash
docker-compose up -d mysql zookeeper kafka
```

### 2. Run Applications Locally
```bash
# Terminal 1 - Producer
cd producer-app
mvn spring-boot:run

# Terminal 2 - Consumer  
cd consumer-app
mvn spring-boot:run

# Terminal 3 - Telegram Bot
cd telegram-bot
mvn spring-boot:run
```

---

## 📊 Testing the System

### 1. Submit YouTube Links
1. Go to http://localhost:8080
2. Submit 5 valid YouTube video URLs
3. Examples:
   - https://www.youtube.com/watch?v=dQw4w9WgXcQ
   - https://www.youtube.com/watch?v=9bZkp7q19f0

### 2. Monitor Results
- **Telegram**: Check your bot for odd-length comments
- **Analytics**: Visit http://localhost:8081 for channel comparisons
- **Logs**: Use `docker-compose logs` to see processing

---

## 🛠️ Troubleshooting

### Common Issues:

1. **"API quota exceeded"**
   - YouTube API has daily limits
   - Wait or use a different API key

2. **"Telegram bot not responding"**
   - Make sure you've started a conversation with your bot
   - Check the bot token is correct

3. **"Database connection failed"**
   - Wait for MySQL to fully start (can take 1-2 minutes)
   - Check Docker logs: `docker-compose logs mysql`

4. **"Kafka connection failed"**
   - Kafka takes time to start
   - Restart applications after Kafka is fully up

### Reset Everything:
```bash
# Stop all services
docker-compose down

# Remove all containers and data
docker-compose down -v

# Start fresh
docker-compose up -d
```

---

## 📝 Project Structure

```
youtube-analytics/
├── docker-compose.yml          # Main orchestration
├── env.example                 # Environment template
├── mysql/
│   └── init.sql               # Database schema
├── shared/                    # Common models
├── producer-app/              # Web app for link submission
├── consumer-app/              # Analytics dashboard
└── telegram-bot/              # Telegram notifications
```

---

## 🎯 Next Steps

Once everything is running:
1. Submit some YouTube video links
2. Wait for the scheduled task to fetch data
3. Check Telegram for notifications
4. View analytics on the dashboard
5. Monitor logs for any issues

Happy coding! 🚀 