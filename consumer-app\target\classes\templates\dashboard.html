<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Analytics Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .metric-card {
            transition: transform 0.2s ease-in-out;
            border-left: 4px solid #007bff;
        }
        .metric-card:hover {
            transform: translateY(-5px);
        }
        .channel-card {
            border-left: 4px solid #28a745;
        }
        .comparison-section {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 15px;
        }
        .comparison-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            transition: transform 0.2s ease-in-out;
        }
        .comparison-card:hover {
            transform: scale(1.05);
            background: rgba(255, 255, 255, 0.2);
        }
        .loading {
            display: none;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .trophy-icon {
            color: #ffd700;
            font-size: 2rem;
        }
        .winner-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #333;
            font-weight: bold;
            border-radius: 20px;
            padding: 0.25rem 0.75rem;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-dark bg-dark">
        <div class="container">
            <span class="navbar-brand mb-0 h1">
                <i class="fab fa-youtube text-danger"></i> YouTube Analytics Dashboard
            </span>
            <div class="d-flex">
                <button class="btn btn-outline-light me-2" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> Refresh
                </button>
                <button class="btn btn-danger" onclick="clearData()">
                    <i class="fas fa-trash"></i> Clear
                </button>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Status Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-tv fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">Total Channels</h5>
                        <h3 class="text-primary" th:text="${stats.totalChannels}">0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-video fa-2x text-success mb-2"></i>
                        <h5 class="card-title">Total Videos</h5>
                        <h3 class="text-success" th:text="${stats.totalVideos}">0</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-database fa-2x text-info mb-2"></i>
                        <h5 class="card-title">Data Status</h5>
                        <h6 class="text-info">
                            <span th:if="${stats.newDataReceived}" class="badge bg-success">New Data</span>
                            <span th:unless="${stats.newDataReceived}" class="badge bg-secondary">Processed</span>
                        </h6>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card metric-card">
                    <div class="card-body text-center">
                        <i class="fas fa-trophy fa-2x text-warning mb-2"></i>
                        <h5 class="card-title">Channel Comparisons</h5>
                        <button class="btn btn-warning btn-sm" onclick="calculateAnalytics()">
                            <i class="fas fa-calculator"></i> Calculate
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Channel Comparisons Section -->
        <div class="row mb-4" id="comparisonsSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                        <h4 class="card-title mb-0">
                            <i class="fas fa-chart-line"></i>
                            Channel Analytics & Comparisons
                        </h4>
                        <button class="btn btn-sm btn-outline-dark" onclick="hideAnalytics()" title="Hide Analytics">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info mb-3">
                            <i class="fas fa-info-circle"></i>
                            <strong>Analytics will stay visible</strong> until you close them or submit new videos.
                            Data refreshes automatically every 30 seconds.
                        </div>
                        <div class="row" id="comparisonsData">
                            <!-- Channel comparisons will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Metadata with Even Comments -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-video"></i> Video Metadata & Even Comments</h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(videos)}" class="text-center text-muted py-5">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <h5>No Data Available</h5>
                            <p>Submit YouTube videos in the Producer App to see analytics here.</p>
                        </div>
                        <div th:unless="${#lists.isEmpty(videos)}" class="row">
                            <div th:each="video : ${videos}" class="col-md-6 mb-3">
                                <div class="card video-card">
                                    <div class="card-body">
                                        <h6 class="card-title" th:text="${video.channelName}">Channel Name</h6>
                                        <small class="text-muted" th:text="${video.videoTitle}">Video Title</small>

                                        <!-- Channel & Video Stats -->
                                        <div class="row text-center mt-3">
                                            <div class="col-6">
                                                <small class="text-muted">Subscribers</small>
                                                <div class="fw-bold" th:text="${video.formattedSubscribers}">0</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Total Videos</small>
                                                <div class="fw-bold" th:text="${video.formattedTotalVideos}">0</div>
                                            </div>
                                        </div>
                                        <div class="row text-center mt-2">
                                            <div class="col-6">
                                                <small class="text-muted">Total Comments</small>
                                                <div class="fw-bold" th:text="${video.formattedTotalComments}">0</div>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Total Likes</small>
                                                <div class="fw-bold" th:text="${video.formattedTotalLikes}">0</div>
                                            </div>
                                        </div>

                                        <!-- Even Comment Section -->
                                        <div class="mt-3">
                                            <small class="text-muted">Even Comment:</small>
                                            <div th:if="${video.hasEvenComment()}" class="alert alert-info mt-1 mb-0">
                                                <small th:text="${video.evenComment}">No even comment available</small>
                                            </div>
                                            <div th:unless="${video.hasEvenComment()}" class="alert alert-warning mt-1 mb-0">
                                                <small>No even comment available for this video</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Modal -->
        <div class="modal fade" id="loadingModal" tabindex="-1">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-body text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <div class="mt-2">Processing...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showLoading() {
            const modal = new bootstrap.Modal(document.getElementById('loadingModal'));
            modal.show();
            return modal;
        }

        function refreshData() {
            const modal = showLoading();
            location.reload();
        }

        function clearData() {
            if (confirm('Are you sure you want to clear all data?')) {
                const modal = showLoading();
                fetch('/clear', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        modal.hide();
                        location.reload();
                    })
                    .catch(error => {
                        modal.hide();
                        alert('Error clearing data: ' + error.message);
                    });
            }
        }

        function calculateAnalytics() {
            const modal = showLoading();
            fetch('/calculate-analytics', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    modal.hide();
                    console.log('Received analytics data:', data); // Debug log
                    displayComparisons(data);
                })
                .catch(error => {
                    modal.hide();
                    console.error('Error calculating analytics:', error);
                    alert('Error calculating analytics: ' + error.message);
                });
        }

        function displayComparisons(data) {
            const comparisonsSection = document.getElementById('comparisonsSection');
            const comparisonsContainer = document.getElementById('comparisonsData');
            
            console.log('=== DEBUG: Processing analytics data ===');
            console.log('Full data object:', JSON.stringify(data, null, 2));
            
            // Check data structure step by step
            console.log('data.status:', data.status);
            console.log('data.analytics exists:', !!data.analytics);
            
            if (data.analytics) {
                console.log('data.analytics keys:', Object.keys(data.analytics));
                console.log('data.analytics.channelComparisons exists:', !!data.analytics.channelComparisons);
                
                if (data.analytics.channelComparisons) {
                    console.log('channelComparisons keys:', Object.keys(data.analytics.channelComparisons));
                    console.log('channelComparisons content:', JSON.stringify(data.analytics.channelComparisons, null, 2));
                }
            }
            
            if (data.status === 'success' && data.analytics && data.analytics.channelComparisons) {
                const comparisons = data.analytics.channelComparisons;
                console.log('=== SHOWING CHANNEL COMPARISONS ===');
                
                comparisonsContainer.innerHTML = `
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card comparison-card">
                            <div class="card-body text-center">
                                <i class="fas fa-users trophy-icon"></i>
                                <h6 class="mt-2">🥇 Most Subscribers</h6>
                                <div class="winner-badge mb-2">${comparisons.mostSubscribers?.channelName || 'N/A'}</div>
                                <div class="text-light">${comparisons.mostSubscribers?.formattedValue || '0'} subscribers</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card comparison-card">
                            <div class="card-body text-center">
                                <i class="fas fa-video trophy-icon"></i>
                                <h6 class="mt-2">🎬 Most Videos</h6>
                                <div class="winner-badge mb-2">${comparisons.mostVideos?.channelName || 'N/A'}</div>
                                <div class="text-light">${comparisons.mostVideos?.formattedValue || '0'} videos</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card comparison-card">
                            <div class="card-body text-center">
                                <i class="fas fa-comments trophy-icon"></i>
                                <h6 class="mt-2">💬 Most Comments</h6>
                                <div class="winner-badge mb-2">${comparisons.mostComments?.channelName || 'N/A'}</div>
                                <div class="text-light">${comparisons.mostComments?.formattedValue || '0'} comments</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card comparison-card">
                            <div class="card-body text-center">
                                <i class="fas fa-heart trophy-icon"></i>
                                <h6 class="mt-2">❤️ Most Likes</h6>
                                <div class="winner-badge mb-2">${comparisons.mostLikes?.channelName || 'N/A'}</div>
                                <div class="text-light">${comparisons.mostLikes?.formattedValue || '0'} likes</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card comparison-card">
                            <div class="card-body text-center">
                                <i class="fas fa-eye trophy-icon"></i>
                                <h6 class="mt-2">👁️ Highest Avg Views</h6>
                                <div class="winner-badge mb-2">${comparisons.highestAvgViews?.channelName || 'N/A'}</div>
                                <div class="text-light">${comparisons.highestAvgViews?.formattedValue || '0'} avg likes/video</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card comparison-card">
                            <div class="card-body text-center">
                                <i class="fas fa-fire trophy-icon"></i>
                                <h6 class="mt-2">🔥 Best Engagement</h6>
                                <div class="winner-badge mb-2">${comparisons.bestEngagement?.channelName || 'N/A'}</div>
                                <div class="text-light">${comparisons.bestEngagement?.formattedValue || '0%'}</div>
                            </div>
                        </div>
                    </div>
                `;
                
                comparisonsSection.style.display = 'block';
                comparisonsSection.classList.add('fade-in');
            } else {
                console.log('=== SHOWING FALLBACK ANALYTICS ===');
                console.log('Reason for fallback:');
                console.log('- data.status === success:', data.status === 'success');
                console.log('- data.analytics exists:', !!data.analytics);
                console.log('- data.analytics.channelComparisons exists:', !!(data.analytics && data.analytics.channelComparisons));
                
                // Fallback: Show general analytics if available
                if (data.status === 'success' && data.analytics) {
                    const analytics = data.analytics;
                    comparisonsContainer.innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-warning" role="alert">
                                <h5>📊 General Analytics Available</h5>
                                <p><strong>Overall Engagement Rate:</strong> ${analytics.engagementRate || 0}%</p>
                                <p><strong>Average Likes per Video:</strong> ${analytics.avgLikesPerVideo || 0}</p>
                                <p><strong>Total Channels:</strong> ${analytics.totalUniqueChannels || 0}</p>
                                <hr>
                                <small>Channel comparison data not available. Make sure videos from different channels are submitted.</small>
                                <br><small><strong>Debug:</strong> Check browser console for detailed information.</small>
                            </div>
                        </div>
                    `;
                    comparisonsSection.style.display = 'block';
                } else {
                    alert('No data available for channel comparisons. Please submit videos first.');
                }
            }
            
            console.log('=== END DEBUG ===');
        }

        // Function to hide analytics display
        function hideAnalytics() {
            const comparisonsSection = document.getElementById('comparisonsSection');
            comparisonsSection.style.display = 'none';
            console.log('Analytics hidden by user');
        }

        // Auto-refresh every 30 seconds (but preserve analytics display)
        let analyticsVisible = false;
        let lastChannelCount = 0;

        setInterval(() => {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    const currentChannelCount = data.consumerStats.totalChannels;

                    // Check if this is a reset (channel count went to 0 or significantly changed)
                    if (currentChannelCount === 0 && lastChannelCount > 0) {
                        console.log('Detected system reset - clearing analytics and reloading');
                        hideAnalytics(); // Hide analytics on reset
                        location.reload();
                        return;
                    }

                    // Check if analytics are currently displayed
                    const comparisonsSection = document.getElementById('comparisonsSection');
                    analyticsVisible = comparisonsSection && comparisonsSection.style.display === 'block';

                    if (data.consumerStats.newDataReceived) {
                        if (!analyticsVisible) {
                            // Only reload if analytics are not being displayed
                            location.reload();
                        } else {
                            // Just refresh the channel data without reloading the page
                            refreshChannelDataOnly();
                        }
                    }

                    lastChannelCount = currentChannelCount;
                })
                .catch(error => console.log('Status check error:', error));
        }, 30000);

        // Function to refresh only video data without affecting analytics display
        function refreshChannelDataOnly() {
            fetch('/api/videos')
                .then(response => response.json())
                .then(videos => {
                    // Update video cards without affecting analytics
                    updateVideoCards(videos);
                })
                .catch(error => console.log('Video refresh error:', error));
        }

        // Function to update video cards
        function updateVideoCards(videos) {
            const videoContainer = document.querySelector('.row:has(.video-card)');
            if (videoContainer && videos.length > 0) {
                // Update video data while preserving analytics display
                console.log('Updating video data while preserving analytics...');
                // You can implement specific video card updates here if needed
            }
        }
    </script>
</body>
</html> 