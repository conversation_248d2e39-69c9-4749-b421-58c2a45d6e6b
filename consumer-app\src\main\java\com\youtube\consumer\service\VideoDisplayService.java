package com.youtube.consumer.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.youtube.consumer.model.VideoDisplayData;
import com.youtube.shared.model.ChannelMetadata;
import com.youtube.shared.model.CommentData;

@Service
public class VideoDisplayService {

    private static final Logger logger = LoggerFactory.getLogger(VideoDisplayService.class);

    private final ChannelMetadataConsumerService channelMetadataConsumerService;
    private final EvenCommentConsumerService evenCommentConsumerService;

    public VideoDisplayService(ChannelMetadataConsumerService channelMetadataConsumerService,
                              EvenCommentConsumerService evenCommentConsumerService) {
        this.channelMetadataConsumerService = channelMetadataConsumerService;
        this.evenCommentConsumerService = evenCommentConsumerService;
    }

    /**
     * Get all video display data combining metadata and even comments
     */
    public List<VideoDisplayData> getAllVideoDisplayData() {
        List<VideoDisplayData> displayDataList = new ArrayList<>();
        
        // Get all video metadata (one entry per video)
        List<ChannelMetadata> allVideoMetadata = channelMetadataConsumerService.getAllChannelMetadata();
        
        logger.debug("Processing {} videos for display", allVideoMetadata.size());
        
        for (ChannelMetadata metadata : allVideoMetadata) {
            String videoId = metadata.getVideoId();
            
            // Get the first even comment for this video
            CommentData evenComment = evenCommentConsumerService.getFirstEvenCommentForVideo(videoId);
            
            // Create display data
            VideoDisplayData displayData = new VideoDisplayData(metadata, evenComment);
            displayDataList.add(displayData);
            
            logger.debug("Created display data for video: {} (has even comment: {})", 
                        videoId, displayData.hasEvenComment());
        }
        
        logger.info("Generated display data for {} videos", displayDataList.size());
        return displayDataList;
    }

    /**
     * Get display data for a specific video
     */
    public VideoDisplayData getVideoDisplayData(String videoId) {
        ChannelMetadata metadata = channelMetadataConsumerService.getVideoMetadata(videoId);
        if (metadata == null) {
            return null;
        }
        
        CommentData evenComment = evenCommentConsumerService.getFirstEvenCommentForVideo(videoId);
        return new VideoDisplayData(metadata, evenComment);
    }

    /**
     * Check if new data is available
     */
    public boolean hasNewData() {
        return channelMetadataConsumerService.hasNewData() || evenCommentConsumerService.hasNewData();
    }

    /**
     * Mark data as processed
     */
    public void markDataAsProcessed() {
        channelMetadataConsumerService.markDataAsProcessed();
        evenCommentConsumerService.markDataAsProcessed();
    }

    /**
     * Get combined statistics
     */
    public Map<String, Object> getStats() {
        Map<String, Object> metadataStats = channelMetadataConsumerService.getStats();
        Map<String, Object> commentStats = evenCommentConsumerService.getStats();
        
        return Map.of(
            "totalChannels", metadataStats.get("totalChannels"),
            "totalVideos", metadataStats.get("totalVideos"),
            "totalVideosWithEvenComments", commentStats.get("totalVideosWithEvenComments"),
            "totalEvenComments", commentStats.get("totalEvenComments"),
            "newDataReceived", hasNewData(),
            "channels", metadataStats.get("channels")
        );
    }

    /**
     * Clear all data
     */
    public void clearAll() {
        channelMetadataConsumerService.clearAll();
        evenCommentConsumerService.clearAll();
        logger.info("Cleared all video display data");
    }
}
