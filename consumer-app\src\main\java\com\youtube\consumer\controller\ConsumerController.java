package com.youtube.consumer.controller;

import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.youtube.consumer.service.AnalyticsService;
import com.youtube.consumer.service.ChannelMetadataConsumerService;
import com.youtube.shared.model.ChannelMetadata;

@Controller
public class ConsumerController {

    private final ChannelMetadataConsumerService channelMetadataConsumerService;
    private final AnalyticsService analyticsService;

    public ConsumerController(ChannelMetadataConsumerService channelMetadataConsumerService, 
                            AnalyticsService analyticsService) {
        this.channelMetadataConsumerService = channelMetadataConsumerService;
        this.analyticsService = analyticsService;
    }

    /**
     * Main dashboard showing metadata and analytics
     */
    @GetMapping("/")
    public String dashboard(Model model) {
        Map<String, Object> stats = channelMetadataConsumerService.getStats();
        Map<String, Object> analyticsStatus = analyticsService.getAnalyticsStatus();
        
        model.addAttribute("stats", stats);
        model.addAttribute("analyticsStatus", analyticsStatus);
        model.addAttribute("channels", channelMetadataConsumerService.getAllChannelMetadata());
        
        return "dashboard";
    }

    /**
     * Get analytics data
     */
    @GetMapping("/analytics")
    @ResponseBody
    public Map<String, Object> getAnalytics() {
        return analyticsService.calculateAnalytics();
    }

    /**
     * Debug endpoint to see raw analytics data
     */
    @GetMapping("/debug-analytics")
    @ResponseBody
    public Map<String, Object> debugAnalytics() {
        Map<String, Object> result = analyticsService.calculateAnalytics();
        System.out.println("Debug Analytics Result: " + result);
        return result;
    }

    /**
     * Test endpoint to add sample data for testing
     */
    @PostMapping("/add-test-data")
    @ResponseBody
    public Map<String, Object> addTestData() {
        try {
            // This would normally come from Kafka, but for testing we'll create sample data
            ChannelMetadata channel1 = new ChannelMetadata(
                "video1", "TechChannel", "channel1", 1000000L, 500L, 10000L, 50000L);
            ChannelMetadata channel2 = new ChannelMetadata(
                "video2", "CookingChannel", "channel2", 500000L, 300L, 15000L, 30000L);
            ChannelMetadata channel3 = new ChannelMetadata(
                "video3", "MusicChannel", "channel3", 2000000L, 800L, 25000L, 100000L);
            
            // Simulate Kafka consumption
            channelMetadataConsumerService.consumeChannelMetadata(channel1);
            channelMetadataConsumerService.consumeChannelMetadata(channel2);
            channelMetadataConsumerService.consumeChannelMetadata(channel3);
            
            return Map.of("status", "success", "message", "Test data added successfully");
        } catch (Exception e) {
            return Map.of("status", "error", "message", e.getMessage());
        }
    }

    /**
     * Get channels data for AJAX refresh
     */
    @GetMapping("/api/channels")
    @ResponseBody
    public List<ChannelMetadata> getChannels() {
        return channelMetadataConsumerService.getAllChannelMetadata();
    }

    /**
     * Get current status
     */
    @GetMapping("/status")
    @ResponseBody
    public Map<String, Object> getStatus() {
        return Map.of(
            "consumerStats", channelMetadataConsumerService.getStats(),
            "analyticsStatus", analyticsService.getAnalyticsStatus()
        );
    }

    /**
     * Clear all data (for testing)
     */
    @PostMapping("/clear")
    @ResponseBody
    public Map<String, Object> clearData() {
        channelMetadataConsumerService.clearAll();
        return Map.of("status", "success", "message", "All data cleared");
    }

    /**
     * Force analytics calculation
     */
    @PostMapping("/calculate-analytics")
    @ResponseBody
    public Map<String, Object> calculateAnalytics() {
        System.out.println("=== DEBUG: Calculate Analytics Called ===");
        
        Map<String, Object> result = analyticsService.calculateAnalytics();
        System.out.println("Calculate Analytics Result: " + result);
        
        return result;
    }
} 